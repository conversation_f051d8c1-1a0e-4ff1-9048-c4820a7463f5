<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置邮箱</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 邮箱配置页面 -->
    <div class="email-config-container">
        <!-- 背景装饰元素 -->
        <div class="email-background-blur"></div>
        <div class="email-background-shapes">
            <div class="bg-shape bg-shape-1"></div>
            <div class="bg-shape bg-shape-2"></div>
        </div>

        <!-- 邮箱配置卡片 -->
        <div class="email-config-card">
            <div class="email-header">
                <!-- 关闭按钮 -->
                <button class="close-btn" onclick="window.close()">×</button>

                <!-- Logo 区域 -->
                <div class="email-logo">
                    <div class="logo-icon-container">
                        <i class="fas fa-envelope logo-icon-main"></i>
                    </div>
                </div>
            </div>

            <div class="email-content">
                <div class="email-title-section">
                    <h1 class="email-main-title">配置邮箱</h1>
                    <p class="email-subtitle">选择邮箱类型并完成配置</p>
                </div>

                <div class="email-form">
                    <!-- 邮箱类型下拉选择 -->
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="input-divider"></div>
                        <select id="email-provider" class="email-input-select">
                            <option value="qq">QQ邮箱</option>
                            <option value="163">163邮箱</option>
                            <option value="other">其他邮箱</option>
                        </select>
                    </div>

                    <!-- 配置指导区域 -->
                    <div class="guidance-wrapper" id="provider-guidance">
                        <div class="guidance-content">
                            <div class="guidance-header">
                                <i class="fas fa-info-circle"></i>
                                <span id="guidance-title">QQ邮箱配置指导</span>
                            </div>
                            <ol id="guidance-steps" class="guidance-steps">
                                <li>登录网页端 → 右上角 设置 → 账户</li>
                                <li>找到「POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV 服务」，点击 开启 IMAP/SMTP</li>
                                <li>系统会弹出短信二次验证，验证后生成一次性 16 位授权码，复制保存，填到下方授权码处</li>
                            </ol>
                            <a href="https://mail.qq.com" target="_blank" class="auth-code-link" id="guidance-link">
                                <i class="fas fa-external-link-alt"></i>
                                点击获取授权码
                            </a>
                        </div>
                    </div>

                    <!-- 邮箱地址输入 -->
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="email" id="email-address" placeholder="例如: <EMAIL>" class="email-input">
                    </div>

                    <!-- 授权码输入 -->
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="password" id="auth-code" placeholder="请输入QQ邮箱授权码" class="email-input">
                    </div>

                    <!-- 自动配置参数 -->
                    <div class="auto-config-section">
                        <div class="config-title">
                            <i class="fas fa-cog"></i>
                            <span>自动配置参数</span>
                        </div>
                        <div class="config-grid">
                            <div class="config-item">
                                <div class="config-label">SMTP服务器</div>
                                <div class="config-value" id="smtp-server">smtp.qq.com</div>
                            </div>
                            <div class="config-item">
                                <div class="config-label">SMTP端口</div>
                                <div class="config-value" id="smtp-port">465 (SSL)</div>
                            </div>
                            <div class="config-item">
                                <div class="config-label">IMAP服务器</div>
                                <div class="config-value" id="imap-server">imap.qq.com</div>
                            </div>
                            <div class="config-item">
                                <div class="config-label">IMAP端口</div>
                                <div class="config-value" id="imap-port">993 (SSL)</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="email-form-actions">
                        <button type="button" class="email-btn-secondary" id="test-btn">测试连接</button>
                        <button type="submit" class="email-btn-primary" id="save-btn">保存配置</button>
                    </div>
                </div>

                <!-- 消息提示容器 -->
                <div id="email-message-container"></div>
            </div>
    </div>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="script.js"></script>
</body>
</html>