/* CSS变量定义 - 与登录页面统一的设计系统 */
:root {
    /* 字体系统 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

    /* 颜色系统 - 与登录页面保持一致 */
    --primary-color: #ffffff;
    --primary-hover: rgba(255, 255, 255, 0.95);
    --primary-text: #1a1a2e;

    /* 文本颜色 */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-tertiary: rgba(255, 255, 255, 0.5);

    /* 背景颜色 */
    --background-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f1419 50%, #1a1a2e 75%, #16213e 100%);
    --card-background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    --surface-color: rgba(255, 255, 255, 0.1);
    --surface-hover: rgba(255, 255, 255, 0.15);

    /* 边框颜色 */
    --border-color: rgba(255, 255, 255, 0.2);
    --border-hover: rgba(255, 255, 255, 0.3);
    --input-border: rgba(255, 255, 255, 0.3);

    /* 圆角 */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 24px;

    /* 阴影 */
    --shadow-primary: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    --shadow-hover: 0 12px 48px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.3);

    /* 动画 */
    --animation-duration: 0.3s;
    --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 移除背景动画以提升性能 */

/* 主容器 - 与登录页面保持一致 */
body {
    font-family: var(--font-family);
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.email-config-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: var(--background-gradient);
    overflow: hidden;
    padding: 20px;
}

/* 静态背景装饰元素 - 简化版本 */
.email-background-blur {
    position: absolute;
    top: -200px;
    left: -200px;
    width: calc(100% + 400px);
    height: calc(100% + 400px);
    background: radial-gradient(circle at 30% 30%, rgba(138, 43, 226, 0.2) 0%, rgba(75, 0, 130, 0.1) 30%, transparent 70%),
                radial-gradient(circle at 70% 70%, rgba(30, 144, 255, 0.2) 0%, rgba(0, 0, 255, 0.1) 30%, transparent 70%);
    filter: blur(100px);
    opacity: 0.6;
    z-index: 1;
}

.email-background-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 2;
}

.bg-shape {
    position: absolute;
    filter: blur(80px);
    opacity: 0.3;
}

.bg-shape-1 {
    top: 20%;
    right: 20%;
    width: 300px;
    height: 200px;
    background: radial-gradient(ellipse, rgba(255, 0, 150, 0.3) 0%, transparent 70%);
}

.bg-shape-2 {
    top: 60%;
    left: 10%;
    width: 400px;
    height: 250px;
    background: radial-gradient(ellipse, rgba(0, 255, 255, 0.25) 0%, transparent 70%);
}

/* 邮箱配置卡片 - 玻璃拟态效果 */
.email-config-card {
    position: relative;
    width: 100%;
    max-width: 750px;
    min-height: 600px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: var(--shadow-primary);
    transition: all var(--animation-duration) var(--animation-easing);
    z-index: 10;
}

.email-config-card:hover {
    box-shadow: var(--shadow-hover);
}

/* 头部区域 */
.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 48px 0;
    position: relative;
}

/* Logo 区域 */
.email-logo {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 24px;
}

.logo-icon-container {
    width: 23.253px;
    height: 18.685px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon-main {
    color: var(--text-primary);
    font-size: 18px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 关闭按钮 */
.close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    transition: all var(--animation-duration) var(--animation-easing);
    font-size: 16px;
    line-height: 1;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
}

/* 内容区域 */
.email-content {
    padding: 40px 48px 48px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32px;
}

/* 标题区域 */
.email-title-section {
    text-align: center;
    margin-bottom: 8px;
}

.email-main-title {
    font-family: var(--font-family);
    font-weight: 700;
    font-size: 32px;
    line-height: 1.2;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.email-subtitle {
    font-family: var(--font-family);
    font-weight: 400;
    font-size: 16px;
    line-height: 1.4;
    color: var(--text-secondary);
    letter-spacing: 0.2px;
    margin: 0;
}

/* 表单区域 */
.email-form {
    width: 100%;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 输入框容器 - 与登录页面保持一致 */
.email-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    height: 56px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--input-border);
    border-radius: var(--radius-md);
    padding: 0 16px;
    gap: 12px;
    transition: all var(--animation-duration) var(--animation-easing);
    backdrop-filter: blur(10px);
}

.email-input-wrapper:hover {
    border-color: var(--border-hover);
    background: rgba(255, 255, 255, 0.12);
}

.email-input-wrapper:focus-within {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

/* 输入框图标 */
.input-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: var(--text-secondary);
    flex-shrink: 0;
}

.input-icon i {
    font-size: 16px;
}

/* 分隔线 */
.input-divider {
    width: 1px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

/* 输入框和选择框 */
.email-input,
.email-input-select {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
}

.email-input::placeholder {
    color: var(--text-tertiary);
    font-family: var(--font-family);
}

.email-input-select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
}

.email-input-select option {
    background-color: #1a1a2e;
    color: var(--text-primary);
    padding: 8px;
}

/* 配置指导区域 */
.guidance-wrapper {
    width: 100%;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--radius-md);
    padding: 20px;
    backdrop-filter: blur(10px);
    margin-bottom: 8px;
}

.guidance-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.guidance-header {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 16px;
}

.guidance-header i {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.guidance-steps {
    padding-left: 20px;
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.6;
}

.guidance-steps li {
    margin-bottom: 8px;
    padding-left: 4px;
}

.auth-code-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all var(--animation-duration) var(--animation-easing);
    backdrop-filter: blur(10px);
}

.auth-code-link:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.35);
}

.auth-code-link i {
    font-size: 12px;
}

/* 自动配置参数区域 */
.auto-config-section {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: 20px;
    backdrop-filter: blur(10px);
}

.config-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 16px;
}

.config-title i {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
}

.config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-label {
    color: var(--text-secondary);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.config-value {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--radius-sm);
    padding: 10px 12px;
    color: var(--text-primary);
    font-size: 14px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    backdrop-filter: blur(5px);
}

/* 操作按钮区域 */
.email-form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    justify-content: center;
    width: 100%;
}

/* 按钮基础样式 - 与登录页面保持一致 */
.email-btn-primary,
.email-btn-secondary {
    height: 56px;
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--animation-duration) var(--animation-easing);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 140px;
    flex: 1;
    max-width: 200px;
}

/* 主要按钮 - 白色按钮，简化动画 */
.email-btn-primary {
    background: var(--primary-color);
    color: var(--primary-text);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.email-btn-primary:hover {
    background: var(--primary-hover);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.email-btn-primary:active {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
}

/* 次要按钮 - 透明按钮，简化动画 */
.email-btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.email-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.email-btn-secondary:active {
    background: rgba(255, 255, 255, 0.05);
}

/* 消息提示样式 - 与登录页面保持一致 */
.email-message {
    padding: 12px 16px;
    border-radius: var(--radius-sm);
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    border-left: 4px solid;
    animation: slideIn 0.3s ease-out;
    backdrop-filter: blur(10px);
}

.email-message-success {
    background: rgba(34, 197, 94, 0.15);
    color: #4ade80;
    border-color: #22c55e;
}

.email-message-error {
    background: rgba(239, 68, 68, 0.15);
    color: #f87171;
    border-color: #ef4444;
}

.email-message-info {
    background: rgba(59, 130, 246, 0.15);
    color: #60a5fa;
    border-color: #3b82f6;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按钮禁用状态 */
.email-btn-primary:disabled,
.email-btn-secondary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.email-btn-primary:disabled:hover,
.email-btn-secondary:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .email-config-card {
        width: 90%;
        max-width: 480px;
        margin: 20px;
    }

    .email-content {
        padding: 32px 24px 40px;
    }

    .email-background-blur {
        filter: blur(60px);
    }

    .bg-shape-1, .bg-shape-2 {
        display: none;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .email-config-container {
        padding: 10px;
    }

    .email-config-card {
        width: 95%;
        margin: 10px;
        min-height: auto;
    }

    .email-content {
        padding: 24px 20px 32px;
        gap: 24px;
    }

    .email-form {
        gap: 16px;
    }

    .email-main-title {
        font-size: 24px;
    }

    .email-subtitle {
        font-size: 14px;
    }

    .email-input-wrapper {
        height: 52px;
        padding: 0 12px;
    }

    .email-btn-primary,
    .email-btn-secondary {
        height: 52px;
        font-size: 15px;
    }

    .email-form-actions {
        flex-direction: column;
        gap: 12px;
    }

    .email-btn-primary,
    .email-btn-secondary {
        max-width: none;
    }

    .guidance-wrapper,
    .auto-config-section {
        padding: 16px;
    }

    .config-grid {
        gap: 12px;
    }
}

